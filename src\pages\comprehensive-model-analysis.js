// Comprehensive analysis of all four models in the Smart Trash AI application
const API_KEY = '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';
const BASE_URL = 'https://openrouter.ai/api/v1/chat/completions';
const SITE_URL = 'https://smart-trash-ai.lovable.app';
const SITE_NAME = 'Smart Trash AI';

// Test scenarios for comprehensive evaluation
const testScenarios = [
  {
    name: 'Correct Sorting - Paper',
    prompt: 'The user correctly sorted "newspaper" into "Paper Bin". Provide encouraging feedback about why this choice was correct.'
  },
  {
    name: 'Incorrect Sorting - Plastic',
    prompt: 'The user incorrectly sorted "plastic bottle" into "Paper Bin", but it belongs in "Plastic Bin". Kindly explain why it belongs in the correct bin and provide helpful tips.'
  },
  {
    name: 'Correct Sorting - Organic',
    prompt: 'The user correctly sorted "banana peel" into "Bio Bin". Provide encouraging feedback about why this choice was correct.'
  }
];

async function testModelWithScenario(model, modelName, scenario) {
  try {
    const systemPrompt = `You are a friendly and knowledgeable AI assistant for waste sorting and recycling.
Your job is to help users properly sort and dispose of waste items.

Respond in a natural, conversational tone as if talking to a friend.
Be encouraging, educational, and helpful. Use emojis sparingly to make your messages friendlier.
Keep your responses concise (2-3 sentences) but informative.

Focus on:
- Why the item belongs in this category
- Practical disposal tips
- Environmental impact or recycling benefits
- Where to dispose of the item`;

    const startTime = Date.now();
    
    const response = await fetch(BASE_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'HTTP-Referer': SITE_URL,
        'X-Title': SITE_NAME,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: scenario.prompt }
        ],
        max_tokens: 150,
        temperature: 0.7
      })
    });

    const responseTime = Date.now() - startTime;

    if (!response.ok) {
      const errorText = await response.text();
      return { 
        success: false, 
        error: `${response.status} ${response.statusText}`,
        responseTime: responseTime
      };
    }

    const data = await response.json();
    const message = data.choices?.[0]?.message?.content;
    const usage = data.usage;

    if (message && message.trim()) {
      return { 
        success: true, 
        response: message.trim(), 
        responseTime: responseTime,
        usage: usage,
        provider: data.provider,
        length: message.trim().length
      };
    } else {
      return { 
        success: false, 
        error: 'No content',
        responseTime: responseTime
      };
    }

  } catch (error) {
    return { 
      success: false, 
      error: error.message,
      responseTime: 0
    };
  }
}

function analyzeResponse(response, scenario) {
  let score = 0;
  const analysis = {};
  
  // Tone analysis
  const encouragingWords = ['great', 'excellent', 'good', 'awesome', 'fantastic', 'well done', 'perfect'];
  const hasEncouraging = encouragingWords.some(word => response.toLowerCase().includes(word));
  analysis.encouraging = hasEncouraging;
  if (hasEncouraging) score += 2;
  
  // Educational content
  const educationalWords = ['recycle', 'environment', 'tree', 'energy', 'waste', 'landfill', 'pollution'];
  const hasEducational = educationalWords.some(word => response.toLowerCase().includes(word));
  analysis.educational = hasEducational;
  if (hasEducational) score += 2;
  
  // Specific information
  const hasSpecific = response.toLowerCase().includes('bin') || 
                     response.toLowerCase().includes('newspaper') ||
                     response.toLowerCase().includes('plastic') ||
                     response.toLowerCase().includes('banana');
  analysis.specific = hasSpecific;
  if (hasSpecific) score += 2;
  
  // Emoji usage
  const emojiCount = (response.match(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu) || []).length;
  analysis.emojiCount = emojiCount;
  if (emojiCount > 0 && emojiCount <= 3) score += 1;
  
  // Length appropriateness
  analysis.length = response.length;
  if (response.length >= 100 && response.length <= 400) score += 2;
  else if (response.length < 100) score += 1;
  
  // Conversational tone
  const hasConversational = response.toLowerCase().includes('you') && 
                           !response.toLowerCase().includes('should not');
  analysis.conversational = hasConversational;
  if (hasConversational) score += 1;
  
  analysis.totalScore = Math.min(score, 10);
  return analysis;
}

async function comprehensiveModelAnalysis() {
  console.log('🚀 COMPREHENSIVE SMART TRASH AI MODEL ANALYSIS');
  console.log('Testing all four models across multiple scenarios');
  console.log('=' .repeat(80));

  const models = [
    { id: 'meta-llama/llama-3.1-8b-instruct:free', name: 'Llama 3.1 8B (Default)', category: 'Large' },
    { id: 'mistralai/mistral-7b-instruct:free', name: 'Mistral 7B', category: 'Medium' },
    { id: 'meta-llama/llama-3.2-3b-instruct:free', name: 'Llama 3.2 3B', category: 'Small' },
    { id: 'qwen/qwen2.5-vl-32b-instruct:free', name: 'Qwen 2.5 VL 32B (New)', category: 'Extra Large' }
  ];

  const results = {};
  
  // Initialize results structure
  models.forEach(model => {
    results[model.id] = {
      name: model.name,
      category: model.category,
      scenarios: {},
      averages: {}
    };
  });

  // Test each model with each scenario
  for (const model of models) {
    console.log(`\n🧪 Testing ${model.name}...`);
    
    for (const scenario of testScenarios) {
      console.log(`  📝 Scenario: ${scenario.name}`);
      
      const result = await testModelWithScenario(model.id, model.name, scenario);
      
      if (result.success) {
        const analysis = analyzeResponse(result.response, scenario);
        results[model.id].scenarios[scenario.name] = {
          ...result,
          analysis: analysis
        };
        console.log(`    ✅ Success (${result.responseTime}ms, ${analysis.totalScore}/10)`);
      } else {
        results[model.id].scenarios[scenario.name] = result;
        console.log(`    ❌ Failed: ${result.error}`);
      }
      
      // Wait between requests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  // Calculate averages for each model
  models.forEach(model => {
    const modelResults = results[model.id];
    const successfulScenarios = Object.values(modelResults.scenarios).filter(s => s.success);
    
    if (successfulScenarios.length > 0) {
      modelResults.averages = {
        responseTime: successfulScenarios.reduce((sum, s) => sum + s.responseTime, 0) / successfulScenarios.length,
        qualityScore: successfulScenarios.reduce((sum, s) => sum + s.analysis.totalScore, 0) / successfulScenarios.length,
        length: successfulScenarios.reduce((sum, s) => sum + s.length, 0) / successfulScenarios.length,
        totalTokens: successfulScenarios.reduce((sum, s) => sum + (s.usage?.total_tokens || 0), 0) / successfulScenarios.length,
        successRate: (successfulScenarios.length / testScenarios.length) * 100
      };
    }
  });

  return results;
}

// Continue with analysis and reporting functions...