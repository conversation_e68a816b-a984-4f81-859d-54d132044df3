import React, { useState } from 'react';
import { ragLLMService } from '@/services/ragLLMService';
import { LLMModel } from '@/contexts/ModelSettingsContext';

const APITester: React.FC = () => {
  const [testResults, setTestResults] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const testSingleModel = async (model: LLMModel) => {
    setIsLoading(true);
    try {
      const result = await ragLLMService.testOpenRouterAPI(model);
      setTestResults({ [model]: result });
    } catch (error) {
      setTestResults({ [model]: { error: error.message } });
    } finally {
      setIsLoading(false);
    }
  };

  const testAllModels = async () => {
    setIsLoading(true);
    try {
      const results = await ragLLMService.testAllModels();
      setTestResults(results);
    } catch (error) {
      setTestResults({ error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-4 bg-gray-100 rounded-lg">
      <h3 className="text-lg font-bold mb-4">OpenRouter API Tester</h3>
      
      <div className="space-y-2 mb-4">
        <button
          onClick={() => testSingleModel('meta-llama/llama-3.1-8b-instruct:free')}
          disabled={isLoading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          Test Llama 3.1 8B
        </button>
        
        <button
          onClick={() => testSingleModel('mistralai/mistral-7b-instruct:free')}
          disabled={isLoading}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 ml-2"
        >
          Test Mistral 7B
        </button>
        
        <button
          onClick={() => testSingleModel('deepseek/deepseek-r1-0528-qwen3-8b:free')}
          disabled={isLoading}
          className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50 ml-2"
        >
          Test DeepSeek R1
        </button>
        
        <button
          onClick={testAllModels}
          disabled={isLoading}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50 ml-2"
        >
          Test All Models
        </button>
      </div>

      {isLoading && <p className="text-blue-600">Testing API...</p>}

      {testResults && (
        <div className="mt-4">
          <h4 className="font-semibold mb-2">Test Results:</h4>
          <pre className="bg-gray-800 text-green-400 p-4 rounded text-xs overflow-auto max-h-96">
            {JSON.stringify(testResults, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default APITester;
