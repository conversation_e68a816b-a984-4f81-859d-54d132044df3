// Use built-in fetch (Node.js 18+)

const API_KEY = '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';
const BASE_URL = 'https://openrouter.ai/api/v1/chat/completions';
const SITE_URL = 'https://smart-trash-ai.lovable.app';
const SITE_NAME = 'Smart Trash AI';

async function testModel(model) {
  console.log(`\n=== Testing model: ${model} ===`);
  
  try {
    const response = await fetch(BASE_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'HTTP-Referer': SITE_URL,
        'X-Title': SITE_NAME,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: model,
        messages: [
          { role: 'user', content: 'Hello, can you respond with a simple greeting?' }
        ],
        max_tokens: 50,
        temperature: 0.7
      })
    });

    console.log(`Response status: ${response.status}`);
    console.log(`Response headers:`, Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API Error: ${response.status} ${response.statusText}`);
      console.error(`Error details:`, errorText);
      return { error: `${response.status} ${response.statusText}`, details: errorText };
    }

    const data = await response.json();
    console.log('Success response:', JSON.stringify(data, null, 2));
    return { success: true, data };

  } catch (error) {
    console.error('Exception:', error.message);
    return { error: error.message };
  }
}

async function testUpdatedModels() {
  console.log('\n=== Testing Updated Model Configuration ===');
  const models = [
    'meta-llama/llama-3.1-8b-instruct:free',  // New default
    'mistralai/mistral-7b-instruct:free',
    'deepseek/deepseek-r1-0528-qwen3-8b:free'
  ];

  for (const model of models) {
    await testModel(model);
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between requests
  }
}

testUpdatedModels().catch(console.error);
